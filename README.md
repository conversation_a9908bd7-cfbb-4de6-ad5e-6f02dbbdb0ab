# AI Sudoku Solver - Fase 1

Un'applicazione web che utilizza l'intelligenza artificiale per riconoscere e risolvere puzzle Sudoku da immagini.

## 🚀 Caratteristiche (Fase 1)

- **Upload Immagini**: Carica immagini di puzzle Sudoku tramite drag & drop o selezione file
- **Riconoscimento AI**: Utilizza Ollama con modelli di visione per riconoscere automaticamente le griglie Sudoku
- **Supporto Multi-Dimensione**: Rileva griglie 9x9, 16x16 e altre varianti
- **Distinzione Numeri**: Distingue tra numeri originali del puzzle e numeri scritti dall'utente
- **Interfaccia Intuitiva**: Design moderno e responsive

## 📋 Prerequisiti

### 1. Installazione Ollama

Scarica e installa Ollama dal sito ufficiale: https://ollama.ai

### 2. Download Modello AI

Dopo aver installato Ollama, scarica uno dei modelli supportati:

```bash
# Modello predefinito (consigliato)
ollama pull gemma3:12b

# Modelli alternativi per visione
ollama pull llava:7b      # Equilibrio prestazioni/velocità
ollama pull llava:13b     # Prestazioni superiori
ollama pull moondream     # Ultraleggero e veloce
```

**Modelli supportati:**
- `gemma3:12b` - **Predefinito** - Ultima versione Gemma, ottime prestazioni
- `llava:7b` - Specializzato per visione, equilibrato
- `llava:13b` - Prestazioni superiori per visione
- `gemma2:9b` / `gemma2:27b` - Modelli Google multimodali
- `moondream` - Leggero e veloce

### 3. Avvio Ollama

Assicurati che Ollama sia in esecuzione:

```bash
ollama serve
```

Ollama dovrebbe essere disponibile su `http://localhost:11434`

## 🛠️ Installazione

1. **Clona o scarica il progetto**
2. **Apri il progetto in un server web locale**

### Opzione A: Usando Python
```bash
cd sudoku
python -m http.server 8000
```

### Opzione B: Usando Node.js
```bash
cd sudoku
npx serve .
```

### Opzione C: Usando PHP (se hai Laragon/XAMPP)
- Copia i file nella cartella `www` del tuo server locale
- Accedi tramite `http://localhost/sudoku`

## 🎯 Come Usare

1. **Avvia l'applicazione** aprendo `index.html` nel browser
2. **Carica un'immagine** di un puzzle Sudoku:
   - Trascina l'immagine nell'area di upload
   - Oppure clicca per selezionare un file
3. **Clicca "Analizza Immagine"** per avviare il riconoscimento AI
4. **Visualizza il risultato** con la griglia riconosciuta

## 📁 Struttura Progetto

```
sudoku/
├── index.html              # Pagina principale
├── styles.css              # Stili CSS
├── js/
│   ├── app.js              # Logica principale applicazione
│   ├── ollama-client.js    # Client per comunicazione con Ollama
│   └── sudoku-grid.js      # Renderer griglia Sudoku
└── README.md               # Questo file
```

## 🔧 Configurazione

### Selezione Modello AI

L'applicazione include un **selettore di modelli integrato**:

1. **Nell'interfaccia principale**: Clicca il pulsante "Cambia" accanto al modello corrente
2. **Scegli tra i modelli disponibili**: Gemma3, LLaVA, Moondream, etc.
3. **Salvataggio automatico**: La tua scelta viene salvata per le sessioni future

**Modifica manuale** (opzionale):
```javascript
// In js/ollama-client.js
this.currentModel = 'gemma3:12b'; // Cambia il modello predefinito
```

### Modifica URL Ollama

Se Ollama è in esecuzione su un altro indirizzo, modifica `baseUrl` nel file `js/ollama-client.js`.

## 🐛 Risoluzione Problemi

### Errore "Ollama non disponibile"
- Verifica che Ollama sia in esecuzione: `ollama serve`
- Controlla che sia accessibile su `http://localhost:11434`

### Errore "Modello non trovato"
- Scarica il modello: `ollama pull llava:7b`
- Verifica i modelli installati: `ollama list`

### Problemi CORS
- Usa un server web locale (non aprire direttamente il file HTML)
- Assicurati che Ollama permetta richieste dal tuo dominio

### Immagine non riconosciuta
- Usa immagini chiare e ben illuminate
- Assicurati che la griglia Sudoku sia ben visibile
- Prova con immagini di dimensioni diverse

## 📊 Formati Supportati

- **Immagini**: JPG, PNG, GIF (max 10MB)
- **Griglie**: 9x9, 16x16 e altre varianti
- **Qualità**: Immagini chiare con buon contrasto

## 🔮 Prossime Fasi

- **Fase 2**: Implementazione algoritmo di risoluzione Sudoku
- **Fase 3**: Modalità interattiva per modifica manuale
- **Fase 4**: Supporto per puzzle più complessi

## 🤝 Contributi

Questo è un progetto in sviluppo. Suggerimenti e miglioramenti sono benvenuti!

## 📄 Licenza

Progetto educativo - Uso libero per scopi di apprendimento.
