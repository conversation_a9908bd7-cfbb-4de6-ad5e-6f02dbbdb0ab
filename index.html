<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Sudoku Solver - Riconoscimento Immagini</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-brain"></i> AI Sudoku Solver</h1>
            <p class="subtitle">Carica un'immagine del tuo puzzle Sudoku e lascia che l'AI lo riconosca</p>

            <!-- Selezione Modello AI -->
            <div class="model-selector">
                <div class="model-info">
                    <span class="model-label">Modello AI:</span>
                    <span class="current-model" id="currentModelDisplay">gemma3:12b</span>
                    <button class="btn-model-select" id="modelSelectBtn">
                        <i class="fas fa-cog"></i> Cambia
                    </button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <!-- Sezione Upload Immagine -->
            <section class="upload-section">
                <div class="upload-container">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h3>Carica Immagine Sudoku</h3>
                            <p>Trascina qui la tua immagine o clicca per selezionare</p>
                            <p class="file-info">Supportati: JPG, PNG, GIF (max 10MB)</p>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" hidden>
                    </div>
                    
                    <!-- Preview Immagine -->
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" src="" alt="Preview">
                        <div class="preview-actions">
                            <button class="btn btn-primary" id="analyzeBtn">
                                <i class="fas fa-search"></i> Analizza Immagine
                            </button>
                            <button class="btn btn-secondary" id="clearBtn">
                                <i class="fas fa-times"></i> Rimuovi
                            </button>
                        </div>
                    </div>

                    <!-- Demo Mode -->
                    <div class="demo-section">
                        <h4><i class="fas fa-play-circle"></i> Modalità Demo</h4>
                        <p>Testa l'applicazione senza caricare un'immagine:</p>
                        <div class="demo-actions">
                            <button class="btn btn-success" id="demoBtn">
                                <i class="fas fa-magic"></i> Demo Riconoscimento
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sezione Status -->
            <section class="status-section" id="statusSection" style="display: none;">
                <div class="status-container">
                    <div class="status-content">
                        <div class="loading-spinner" id="loadingSpinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Analizzando immagine con AI...</span>
                        </div>
                        <div class="status-message" id="statusMessage"></div>
                    </div>
                </div>
            </section>

            <!-- Sezione Griglia Sudoku -->
            <section class="sudoku-section" id="sudokuSection" style="display: none;">
                <div class="sudoku-container">
                    <div class="sudoku-header">
                        <h2>Griglia Riconosciuta</h2>
                        <div class="grid-info">
                            <span class="grid-size" id="gridSize">9x9</span>
                            <span class="confidence" id="confidence">Confidenza: --</span>
                        </div>
                    </div>
                    
                    <!-- Griglia Sudoku -->
                    <div class="sudoku-grid" id="sudokuGrid">
                        <!-- La griglia verrà generata dinamicamente -->
                    </div>
                    
                    <!-- Legenda -->
                    <div class="legend">
                        <div class="legend-item">
                            <span class="legend-sample original">8</span>
                            <span>Numeri originali (rilevati dall'immagine)</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-sample user">3</span>
                            <span>Numeri scritti dall'utente</span>
                        </div>
                    </div>
                    
                    <!-- Azioni -->
                    <div class="sudoku-actions">
                        <button class="btn btn-success" id="solveBtn" disabled>
                            <i class="fas fa-magic"></i> Risolvi Sudoku
                        </button>
                        <button class="btn btn-secondary" id="resetBtn">
                            <i class="fas fa-undo"></i> Nuova Immagine
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2024 AI Sudoku Solver - Powered by Ollama</p>
        </footer>
    </div>

    <!-- Modal Selezione Modello -->
    <div class="modal" id="modelModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-robot"></i> Seleziona Modello AI</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body">
                <p>Scegli il modello AI da utilizzare per il riconoscimento Sudoku:</p>
                <div class="model-list" id="modelList">
                    <!-- Lista modelli generata dinamicamente -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">Annulla</button>
                <button class="btn btn-primary" id="modalConfirm">Conferma</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/demo-data.js"></script>
    <script src="js/ollama-client.js"></script>
    <script src="js/sudoku-grid.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
