// App principale per AI Sudoku Solver
class SudokuApp {
    constructor() {
        this.currentImage = null;
        this.recognizedGrid = null;
        this.ollamaClient = new OllamaClient();
        this.sudokuGrid = new SudokuGridRenderer();
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // Upload elements
        this.uploadArea = document.getElementById('uploadArea');
        this.imageInput = document.getElementById('imageInput');
        this.imagePreview = document.getElementById('imagePreview');
        this.previewImg = document.getElementById('previewImg');
        
        // Buttons
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.solveBtn = document.getElementById('solveBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.demoBtn = document.getElementById('demoBtn');
        
        // Status elements
        this.statusSection = document.getElementById('statusSection');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.statusMessage = document.getElementById('statusMessage');
        
        // Sudoku elements
        this.sudokuSection = document.getElementById('sudokuSection');
        this.gridSize = document.getElementById('gridSize');
        this.confidence = document.getElementById('confidence');
        this.sudokuGridElement = document.getElementById('sudokuGrid');
    }

    bindEvents() {
        // Upload area events
        this.uploadArea.addEventListener('click', () => this.imageInput.click());
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        
        // File input change
        this.imageInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Button events
        this.analyzeBtn.addEventListener('click', this.analyzeImage.bind(this));
        this.clearBtn.addEventListener('click', this.clearImage.bind(this));
        this.resetBtn.addEventListener('click', this.resetApp.bind(this));
        this.solveBtn.addEventListener('click', this.solveSudoku.bind(this));
        this.demoBtn.addEventListener('click', this.runDemo.bind(this));
    }

    // Drag and Drop handlers
    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        // Validazione file
        if (!this.validateFile(file)) {
            return;
        }

        this.currentImage = file;
        this.displayImagePreview(file);
    }

    validateFile(file) {
        // Controlla tipo file
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            this.showError('Tipo di file non supportato. Usa JPG, PNG o GIF.');
            return false;
        }

        // Controlla dimensione (10MB max)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.showError('File troppo grande. Dimensione massima: 10MB.');
            return false;
        }

        return true;
    }

    displayImagePreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.previewImg.src = e.target.result;
            this.imagePreview.style.display = 'block';
            this.uploadArea.style.display = 'none';
        };
        reader.readAsDataURL(file);
    }

    clearImage() {
        this.currentImage = null;
        this.imagePreview.style.display = 'none';
        this.uploadArea.style.display = 'block';
        this.imageInput.value = '';
        this.hideStatus();
        this.hideSudokuSection();
    }

    async analyzeImage() {
        if (!this.currentImage) {
            this.showError('Nessuna immagine selezionata.');
            return;
        }

        try {
            this.showLoading('Analizzando immagine con AI...');

            // Converti immagine in base64
            const base64Image = await this.fileToBase64(this.currentImage);

            // Chiama Ollama per analizzare l'immagine
            const result = await this.ollamaClient.analyzeImage(base64Image);

            if (result.success) {
                this.recognizedGrid = result.data;
                this.displayRecognizedGrid(result.data);
                this.showSuccess('Griglia riconosciuta con successo!');
            } else {
                // Se Ollama non è disponibile, offri modalità demo
                if (result.error.includes('Ollama non è disponibile')) {
                    this.showError(result.error + ' <br><br><button class="btn btn-success" onclick="app.runDemo()">Prova la Demo</button>');
                } else {
                    this.showError(result.error || 'Errore durante il riconoscimento.');
                }
            }

        } catch (error) {
            console.error('Errore analisi immagine:', error);
            this.showError('Errore durante l\'analisi dell\'immagine.');
        } finally {
            this.hideLoading();
        }
    }

    async runDemo() {
        try {
            this.showLoading('Simulando riconoscimento AI...');

            // Simula il riconoscimento con dati demo
            const result = await DemoData.simulateAnalysis(2000);

            if (result.success) {
                this.recognizedGrid = result.data;
                this.displayRecognizedGrid(result.data);
                this.showSuccess('Demo completata! Griglia di esempio caricata.');
            }

        } catch (error) {
            console.error('Errore demo:', error);
            this.showError('Errore durante la demo.');
        } finally {
            this.hideLoading();
        }
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // Rimuovi il prefisso data:image/...;base64,
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    displayRecognizedGrid(gridData) {
        // Aggiorna informazioni griglia
        this.gridSize.textContent = `${gridData.size}x${gridData.size}`;
        this.confidence.textContent = `Confidenza: ${gridData.confidence}%`;
        
        // Renderizza griglia
        this.sudokuGrid.renderGrid(this.sudokuGridElement, gridData);
        
        // Mostra sezione Sudoku
        this.sudokuSection.style.display = 'block';
        
        // Abilita pulsante risolvi (per fase futura)
        // this.solveBtn.disabled = false;
    }

    solveSudoku() {
        // Placeholder per fase futura
        this.showError('Funzionalità di risoluzione non ancora implementata (Fase 2).');
    }

    resetApp() {
        this.clearImage();
        this.recognizedGrid = null;
    }

    // Utility methods per status
    showLoading(message) {
        this.statusSection.style.display = 'block';
        this.loadingSpinner.style.display = 'flex';
        this.loadingSpinner.querySelector('span').textContent = message;
        this.statusMessage.style.display = 'none';
    }

    hideLoading() {
        this.loadingSpinner.style.display = 'none';
    }

    showSuccess(message) {
        this.statusMessage.textContent = message;
        this.statusMessage.className = 'status-message success';
        this.statusMessage.style.display = 'block';
    }

    showError(message) {
        this.statusMessage.innerHTML = message; // Usa innerHTML per supportare HTML
        this.statusMessage.className = 'status-message error';
        this.statusMessage.style.display = 'block';
        this.statusSection.style.display = 'block';
    }

    hideStatus() {
        this.statusSection.style.display = 'none';
    }

    hideSudokuSection() {
        this.sudokuSection.style.display = 'none';
    }
}

// Inizializza app quando DOM è pronto
let app; // Variabile globale per accesso da HTML
document.addEventListener('DOMContentLoaded', () => {
    app = new SudokuApp();
});
