// Client per comunicare con Ollama
class OllamaClient {
    constructor() {
        this.baseUrl = 'http://localhost:11434';
        this.model = 'llava:7b'; // Modello consigliato per visione
    }

    async analyzeImage(base64Image) {
        try {
            // Prima verifica se Ollama è disponibile
            const isAvailable = await this.checkOllamaAvailability();
            if (!isAvailable) {
                return {
                    success: false,
                    error: 'Ollama non è disponibile. Assicurati che sia in esecuzione su localhost:11434'
                };
            }

            // Verifica se il modello è disponibile
            const hasModel = await this.checkModelAvailability();
            if (!hasModel) {
                return {
                    success: false,
                    error: `Modello ${this.model} non trovato. Esegui: ollama pull ${this.model}`
                };
            }

            // Prompt per riconoscimento Sudoku
            const prompt = this.createSudokuPrompt();

            // Chiamata API a Ollama
            const response = await fetch(`${this.baseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: this.model,
                    prompt: prompt,
                    images: [base64Image],
                    stream: false,
                    options: {
                        temperature: 0.1, // Bassa temperatura per risultati più deterministici
                        top_p: 0.9
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Processa la risposta
            return this.processSudokuResponse(data.response);

        } catch (error) {
            console.error('Errore chiamata Ollama:', error);
            return {
                success: false,
                error: `Errore di connessione: ${error.message}`
            };
        }
    }

    async checkOllamaAvailability() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`, {
                method: 'GET',
                timeout: 5000
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    async checkModelAvailability() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`);
            if (!response.ok) return false;
            
            const data = await response.json();
            return data.models.some(model => model.name.includes(this.model.split(':')[0]));
        } catch (error) {
            return false;
        }
    }

    createSudokuPrompt() {
        return `Analizza attentamente questa immagine di un puzzle Sudoku. Devi identificare:

1. La dimensione della griglia (9x9, 16x16, etc.)
2. Tutti i numeri presenti nelle celle
3. Distinguere tra numeri stampati (originali del puzzle) e numeri scritti a mano (dall'utente)

Restituisci ESCLUSIVAMENTE un oggetto JSON valido in questo formato:

{
  "size": [numero intero: dimensione griglia, es. 9 per 9x9],
  "confidence": [numero intero 0-100: quanto sei sicuro del riconoscimento],
  "grid": [array bidimensionale: numeri riconosciuti, 0 per celle vuote],
  "original_numbers": [array di coordinate [riga, colonna] dei numeri stampati/originali],
  "user_numbers": [array di coordinate [riga, colonna] dei numeri scritti a mano]
}

REGOLE CRITICHE:
- Restituisci SOLO il JSON, nessun testo aggiuntivo
- Le coordinate sono 0-based (iniziano da 0)
- Numeri stampati = font tipografico, linee precise
- Numeri scritti a mano = tratto irregolare, scrittura manuale
- Se la griglia non è chiara, imposta confidence < 50
- Controlla attentamente ogni cella

Esempio output per griglia 9x9:
{
  "size": 9,
  "confidence": 85,
  "grid": [[5,3,0,0,7,0,0,0,0],[6,0,0,1,9,5,0,0,0],[0,9,8,0,0,0,0,6,0],[8,0,0,0,6,0,0,0,3],[4,0,0,8,0,3,0,0,1],[7,0,0,0,2,0,0,0,6],[0,6,0,0,0,0,2,8,0],[0,0,0,4,1,9,0,0,5],[0,0,0,0,8,0,0,7,9]],
  "original_numbers": [[0,0],[0,1],[0,4],[1,0],[1,3],[1,4],[1,5],[2,1],[2,2],[2,7]],
  "user_numbers": [[3,1],[4,2]]
}`;
    }

    processSudokuResponse(response) {
        try {
            // Pulisci la risposta da eventuali caratteri extra
            let cleanResponse = response.trim();
            
            // Cerca il JSON nella risposta
            const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('Nessun JSON valido trovato nella risposta');
            }

            const jsonData = JSON.parse(jsonMatch[0]);

            // Validazione dati
            if (!this.validateSudokuData(jsonData)) {
                throw new Error('Dati Sudoku non validi');
            }

            return {
                success: true,
                data: jsonData
            };

        } catch (error) {
            console.error('Errore parsing risposta:', error);
            return {
                success: false,
                error: `Errore nell'interpretazione della risposta: ${error.message}`
            };
        }
    }

    validateSudokuData(data) {
        // Controlla campi obbligatori
        if (!data.size || !data.confidence || !data.grid) {
            return false;
        }

        // Controlla dimensioni griglia
        const validSizes = [4, 6, 9, 16]; // Dimensioni Sudoku comuni
        if (!validSizes.includes(data.size)) {
            return false;
        }

        // Controlla che la griglia abbia le dimensioni corrette
        if (data.grid.length !== data.size) {
            return false;
        }

        for (let row of data.grid) {
            if (!Array.isArray(row) || row.length !== data.size) {
                return false;
            }
        }

        // Controlla confidenza
        if (data.confidence < 0 || data.confidence > 100) {
            return false;
        }

        return true;
    }

    // Metodo per testare la connessione
    async testConnection() {
        const result = await this.checkOllamaAvailability();
        return {
            available: result,
            model: this.model,
            baseUrl: this.baseUrl
        };
    }
}
