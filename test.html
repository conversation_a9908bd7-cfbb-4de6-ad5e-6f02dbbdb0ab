<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Sudoku Solver</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test AI Sudoku Solver</h1>
        <p>Questa pagina ti aiuta a verificare che tutto sia configurato correttamente.</p>

        <div class="test-section">
            <h3>1. Test Connessione Ollama</h3>
            <p>Verifica che Ollama sia in esecuzione e accessibile.</p>
            <button onclick="testOllamaConnection()">Test Connessione</button>
            <div id="ollamaStatus"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Modello AI</h3>
            <p>Verifica che il modello selezionato sia installato e disponibile.</p>
            <p><strong>Modello corrente:</strong> <span id="currentTestModel">gemma3:12b</span></p>
            <button onclick="testModel()">Test Modello</button>
            <div id="modelStatus"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Completo</h3>
            <p>Esegue tutti i test in sequenza.</p>
            <button onclick="runAllTests()">Esegui Tutti i Test</button>
            <div id="allTestsStatus"></div>
        </div>

        <div class="test-section">
            <h3>4. Istruzioni Setup</h3>
            <div class="code">
                # 1. Installa Ollama (se non già fatto)
                # Vai su https://ollama.ai e scarica per il tuo OS
                
                # 2. Scarica il modello (esempio)
                ollama pull gemma3:12b
                # oppure: ollama pull llava:7b
                
                # 3. Avvia Ollama
                ollama serve
                
                # 4. Verifica che sia in esecuzione
                # Apri http://localhost:11434 nel browser
            </div>
        </div>

        <div class="test-section">
            <h3>5. Vai all'Applicazione</h3>
            <p>Se tutti i test passano, puoi usare l'applicazione principale:</p>
            <button onclick="window.location.href='index.html'">Apri AI Sudoku Solver</button>
        </div>
    </div>

    <script>
        async function testOllamaConnection() {
            const statusDiv = document.getElementById('ollamaStatus');
            statusDiv.innerHTML = '<div class="status info">Testing connessione...</div>';
            
            try {
                const response = await fetch('http://localhost:11434/api/tags');
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="status success">✅ Ollama è in esecuzione e accessibile!</div>';
                    return true;
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ Ollama risponde ma con errore HTTP: ' + response.status + '</div>';
                    return false;
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ Impossibile connettersi a Ollama. Assicurati che sia in esecuzione su localhost:11434</div>';
                return false;
            }
        }

        async function testModel() {
            const statusDiv = document.getElementById('modelStatus');
            const currentModel = document.getElementById('currentTestModel').textContent;
            statusDiv.innerHTML = '<div class="status info">Verificando modello ' + currentModel + '...</div>';

            try {
                const response = await fetch('http://localhost:11434/api/tags');
                if (!response.ok) {
                    statusDiv.innerHTML = '<div class="status error">❌ Impossibile verificare i modelli. Ollama non risponde.</div>';
                    return false;
                }

                const data = await response.json();
                const modelBase = currentModel.split(':')[0];
                const hasModel = data.models.some(model => model.name.includes(modelBase));

                if (hasModel) {
                    const foundModels = data.models.filter(model => model.name.includes(modelBase));
                    statusDiv.innerHTML = '<div class="status success">✅ Modello ' + modelBase + ' trovato: ' + foundModels.map(m => m.name).join(', ') + '</div>';
                    return true;
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ Modello ' + currentModel + ' non trovato. Esegui: <code>ollama pull ' + currentModel + '</code></div>';
                    return false;
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ Errore durante la verifica del modello: ' + error.message + '</div>';
                return false;
            }
        }

        async function runAllTests() {
            const statusDiv = document.getElementById('allTestsStatus');
            statusDiv.innerHTML = '<div class="status info">Eseguendo tutti i test...</div>';
            
            const ollamaOk = await testOllamaConnection();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Pausa tra test
            
            const modelOk = await testModel();
            
            if (ollamaOk && modelOk) {
                statusDiv.innerHTML = '<div class="status success">🎉 Tutti i test passati! L\'applicazione è pronta per l\'uso.</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ Alcuni test sono falliti. Controlla la configurazione sopra.</div>';
            }
        }

        // Esegui test automatico all'avvio
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
