/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Model Selector */
.model-selector {
    margin-top: 20px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.model-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.model-label {
    font-weight: 600;
    color: rgba(255,255,255,0.9);
}

.current-model {
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-family: monospace;
    font-weight: 600;
    color: white;
}

.btn-model-select {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-model-select:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Upload Section */
.upload-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background: #e8ebff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 5px;
}

.file-info {
    font-size: 0.9rem;
    color: #999;
}

/* Image Preview */
.image-preview {
    margin-top: 20px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.preview-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Demo Section */
.demo-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9ff;
    border-radius: 10px;
    border: 2px dashed #667eea;
    text-align: center;
}

.demo-section h4 {
    color: #667eea;
    margin-bottom: 10px;
}

.demo-section p {
    color: #666;
    margin-bottom: 15px;
}

.demo-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Section */
.status-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-size: 1.1rem;
    color: #667eea;
}

.loading-spinner i {
    font-size: 1.5rem;
}

.status-message {
    font-size: 1.1rem;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Sudoku Section */
.sudoku-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.sudoku-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.sudoku-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.grid-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.grid-size {
    background: #667eea;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.confidence {
    color: #666;
    font-size: 0.9rem;
}

/* Sudoku Grid Styles */
.sudoku-grid {
    margin: 20px 0;
}

.legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #666;
}

.legend-sample {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-weight: 600;
}

.legend-sample.original {
    font-weight: 900;
    color: #1a1a1a;
    background-color: #f8f9fa;
}

.legend-sample.user {
    font-weight: 500;
    color: #007bff;
    background-color: #fff;
}

.sudoku-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

/* Highlight animation per celle */
@keyframes highlight {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: inherit; }
}

.sudoku-cell.highlight {
    animation: highlight 1s ease-in-out;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: #f5f5f5;
    color: #333;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.model-list {
    display: grid;
    gap: 15px;
    margin-top: 20px;
}

.model-option {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.model-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.model-option.selected {
    border-color: #667eea;
    background: #f0f2ff;
}

.model-option.selected::after {
    content: '✓';
    position: absolute;
    top: 15px;
    right: 15px;
    color: #667eea;
    font-weight: bold;
    font-size: 1.2rem;
}

.model-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 5px;
}

.model-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.model-details {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
}

.model-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 8px;
    border-radius: 12px;
}

.model-size {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 3px 8px;
    border-radius: 12px;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .model-info {
        flex-direction: column;
        gap: 10px;
    }

    .upload-area {
        padding: 30px 20px;
    }

    .preview-actions {
        flex-direction: column;
        align-items: center;
    }

    .sudoku-header {
        flex-direction: column;
        text-align: center;
    }

    .grid-info {
        justify-content: center;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
    }
}
