/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Upload Section */
.upload-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background: #e8ebff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 5px;
}

.file-info {
    font-size: 0.9rem;
    color: #999;
}

/* Image Preview */
.image-preview {
    margin-top: 20px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.preview-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Demo Section */
.demo-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9ff;
    border-radius: 10px;
    border: 2px dashed #667eea;
    text-align: center;
}

.demo-section h4 {
    color: #667eea;
    margin-bottom: 10px;
}

.demo-section p {
    color: #666;
    margin-bottom: 15px;
}

.demo-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Section */
.status-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-size: 1.1rem;
    color: #667eea;
}

.loading-spinner i {
    font-size: 1.5rem;
}

.status-message {
    font-size: 1.1rem;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Sudoku Section */
.sudoku-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.sudoku-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.sudoku-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.grid-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.grid-size {
    background: #667eea;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.confidence {
    color: #666;
    font-size: 0.9rem;
}

/* Sudoku Grid Styles */
.sudoku-grid {
    margin: 20px 0;
}

.legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #666;
}

.legend-sample {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-weight: 600;
}

.legend-sample.original {
    font-weight: 900;
    color: #1a1a1a;
    background-color: #f8f9fa;
}

.legend-sample.user {
    font-weight: 500;
    color: #007bff;
    background-color: #fff;
}

.sudoku-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

/* Highlight animation per celle */
@keyframes highlight {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: inherit; }
}

.sudoku-cell.highlight {
    animation: highlight 1s ease-in-out;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 30px 20px;
    }
    
    .preview-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .sudoku-header {
        flex-direction: column;
        text-align: center;
    }
    
    .grid-info {
        justify-content: center;
    }
}
