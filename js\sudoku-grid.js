// Renderer per la griglia Sudoku
class SudokuGridRenderer {
    constructor() {
        this.gridData = null;
    }

    renderGrid(container, gridData) {
        this.gridData = gridData;
        
        // Pulisci container
        container.innerHTML = '';
        
        // Crea griglia CSS
        this.createGridCSS(gridData.size);
        
        // Crea elementi griglia
        const gridElement = this.createGridElement(gridData);
        container.appendChild(gridElement);
    }

    createGridCSS(size) {
        // Rimuovi stili precedenti se esistono
        const existingStyle = document.getElementById('sudoku-grid-style');
        if (existingStyle) {
            existingStyle.remove();
        }

        // Crea nuovo stile
        const style = document.createElement('style');
        style.id = 'sudoku-grid-style';
        
        // Calcola dimensioni blocchi
        const blockSize = Math.sqrt(size);
        const isValidBlock = Number.isInteger(blockSize);
        
        style.textContent = `
            .sudoku-grid-container {
                display: grid;
                grid-template-columns: repeat(${size}, 1fr);
                grid-template-rows: repeat(${size}, 1fr);
                gap: 1px;
                background-color: #333;
                border: 3px solid #333;
                border-radius: 8px;
                max-width: 600px;
                margin: 0 auto;
                aspect-ratio: 1;
            }
            
            .sudoku-cell {
                background: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: clamp(1rem, 4vw, 1.5rem);
                font-weight: 600;
                min-height: 40px;
                position: relative;
                transition: background-color 0.2s ease;
            }
            
            .sudoku-cell:hover {
                background-color: #f0f8ff;
            }
            
            .sudoku-cell.original {
                font-weight: 900;
                color: #1a1a1a;
                background-color: #f8f9fa;
            }
            
            .sudoku-cell.user {
                font-weight: 500;
                color: #007bff;
                background-color: #fff;
            }
            
            .sudoku-cell.empty {
                background-color: #fafafa;
                color: #ccc;
            }
            
            ${isValidBlock ? this.generateBlockBorders(size, blockSize) : ''}
            
            /* Responsive */
            @media (max-width: 768px) {
                .sudoku-grid-container {
                    max-width: 90vw;
                }
                
                .sudoku-cell {
                    font-size: clamp(0.8rem, 3vw, 1.2rem);
                    min-height: 35px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    generateBlockBorders(size, blockSize) {
        let css = '';
        
        // Bordi orizzontali per separare i blocchi
        for (let i = blockSize; i < size; i += blockSize) {
            for (let j = 0; j < size; j++) {
                const cellIndex = i * size + j;
                css += `.sudoku-cell:nth-child(${cellIndex + 1}) { border-top: 2px solid #333; }\n`;
            }
        }
        
        // Bordi verticali per separare i blocchi
        for (let i = 0; i < size; i++) {
            for (let j = blockSize; j < size; j += blockSize) {
                const cellIndex = i * size + j;
                css += `.sudoku-cell:nth-child(${cellIndex + 1}) { border-left: 2px solid #333; }\n`;
            }
        }
        
        return css;
    }

    createGridElement(gridData) {
        const gridContainer = document.createElement('div');
        gridContainer.className = 'sudoku-grid-container';
        
        // Crea celle
        for (let row = 0; row < gridData.size; row++) {
            for (let col = 0; col < gridData.size; col++) {
                const cell = this.createCell(row, col, gridData);
                gridContainer.appendChild(cell);
            }
        }
        
        return gridContainer;
    }

    createCell(row, col, gridData) {
        const cell = document.createElement('div');
        cell.className = 'sudoku-cell';
        cell.dataset.row = row;
        cell.dataset.col = col;
        
        const value = gridData.grid[row][col];
        
        if (value === 0) {
            // Cella vuota
            cell.classList.add('empty');
            cell.textContent = '';
        } else {
            // Cella con numero
            cell.textContent = value;
            
            // Determina se è numero originale o dell'utente
            if (this.isOriginalNumber(row, col, gridData)) {
                cell.classList.add('original');
                cell.title = 'Numero originale del puzzle';
            } else {
                cell.classList.add('user');
                cell.title = 'Numero scritto dall\'utente';
            }
        }
        
        return cell;
    }

    isOriginalNumber(row, col, gridData) {
        // Controlla se le coordinate sono nell'array dei numeri originali
        if (!gridData.original_numbers) return true; // Default: considera originale
        
        return gridData.original_numbers.some(coord => 
            coord[0] === row && coord[1] === col
        );
    }

    // Metodi utility per interazione futura
    getCellValue(row, col) {
        if (!this.gridData || !this.gridData.grid[row]) return null;
        return this.gridData.grid[row][col];
    }

    setCellValue(row, col, value) {
        if (!this.gridData || !this.gridData.grid[row]) return false;
        
        this.gridData.grid[row][col] = value;
        
        // Aggiorna visualmente la cella
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            if (value === 0) {
                cell.textContent = '';
                cell.className = 'sudoku-cell empty';
            } else {
                cell.textContent = value;
                cell.className = 'sudoku-cell user';
            }
        }
        
        return true;
    }

    getGridData() {
        return this.gridData;
    }

    // Metodo per evidenziare celle (utile per debugging o feedback)
    highlightCell(row, col, className = 'highlight') {
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            cell.classList.add(className);
            setTimeout(() => cell.classList.remove(className), 2000);
        }
    }

    // Metodo per validare la griglia (per uso futuro)
    validateGrid() {
        if (!this.gridData) return false;
        
        const size = this.gridData.size;
        const grid = this.gridData.grid;
        
        // Validazione base: controlla che non ci siano duplicati in righe/colonne
        for (let i = 0; i < size; i++) {
            const row = grid[i].filter(val => val !== 0);
            const col = grid.map(r => r[i]).filter(val => val !== 0);
            
            if (new Set(row).size !== row.length || new Set(col).size !== col.length) {
                return false;
            }
        }
        
        return true;
    }
}
