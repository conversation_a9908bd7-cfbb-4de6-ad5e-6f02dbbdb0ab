// Dati demo per testare l'applicazione senza Ollama
class DemoData {
    static getSampleSudoku9x9() {
        return {
            size: 9,
            confidence: 92,
            grid: [
                [5, 3, 0, 0, 7, 0, 0, 0, 0],
                [6, 0, 0, 1, 9, 5, 0, 0, 0],
                [0, 9, 8, 0, 0, 0, 0, 6, 0],
                [8, 0, 0, 0, 6, 0, 0, 0, 3],
                [4, 0, 0, 8, 0, 3, 0, 0, 1],
                [7, 0, 0, 0, 2, 0, 0, 0, 6],
                [0, 6, 0, 0, 0, 0, 2, 8, 0],
                [0, 0, 0, 4, 1, 9, 0, 0, 5],
                [0, 0, 0, 0, 8, 0, 0, 7, 9]
            ],
            original_numbers: [
                [0, 0], [0, 1], [0, 4],           // Prima riga: 5, 3, 7
                [1, 0], [1, 3], [1, 4], [1, 5],   // Seconda riga: 6, 1, 9, 5
                [2, 1], [2, 2], [2, 7],           // Terza riga: 9, 8, 6
                [3, 0], [3, 4], [3, 8],           // Quarta riga: 8, 6, 3
                [4, 0], [4, 3], [4, 5], [4, 8],   // Quinta riga: 4, 8, 3, 1
                [5, 0], [5, 4], [5, 8],           // Sesta riga: 7, 2, 6
                [6, 1], [6, 6], [6, 7],           // Settima riga: 6, 2, 8
                [7, 3], [7, 4], [7, 5], [7, 8],   // Ottava riga: 4, 1, 9, 5
                [8, 4], [8, 7], [8, 8]            // Nona riga: 8, 7, 9
            ],
            user_numbers: [
                // Esempi di numeri scritti dall'utente (per demo)
                [2, 3], [3, 2], [4, 1]
            ]
        };
    }

    static getSampleSudoku16x16() {
        return {
            size: 16,
            confidence: 78,
            grid: [
                [1, 0, 0, 4, 0, 6, 0, 8, 0, 10, 0, 12, 0, 14, 0, 16],
                [0, 2, 3, 0, 5, 0, 7, 0, 9, 0, 11, 0, 13, 0, 15, 0],
                [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                [4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0],
                [6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3],
                [0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0],
                [8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5],
                [0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0],
                [10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7],
                [0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0],
                [12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9],
                [0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0],
                [14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11],
                [0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0],
                [16, 0, 0, 1, 0, 3, 0, 5, 0, 7, 0, 9, 0, 11, 0, 13]
            ],
            original_numbers: [
                [0, 0], [0, 3], [0, 5], [0, 7], [0, 9], [0, 11], [0, 13], [0, 15],
                [1, 1], [1, 2], [1, 4], [1, 6], [1, 8], [1, 10], [1, 12], [1, 14],
                [3, 0], [3, 15],
                [4, 1], [4, 14],
                [5, 0], [5, 15],
                [6, 2], [6, 13],
                [7, 0], [7, 15],
                [8, 1], [8, 14],
                [9, 0], [9, 15],
                [10, 2], [10, 13],
                [11, 0], [11, 15],
                [12, 1], [12, 14],
                [13, 0], [13, 15],
                [14, 2], [14, 13],
                [15, 0], [15, 3], [15, 5], [15, 7], [15, 9], [15, 11], [15, 13], [15, 15]
            ],
            user_numbers: []
        };
    }

    static getRandomSample() {
        const samples = [
            this.getSampleSudoku9x9(),
            this.getSampleSudoku16x16()
        ];
        return samples[Math.floor(Math.random() * samples.length)];
    }

    static simulateAnalysis(delay = 2000) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: this.getRandomSample()
                });
            }, delay);
        });
    }
}
